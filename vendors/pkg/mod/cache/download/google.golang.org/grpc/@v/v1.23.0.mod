module google.golang.org/grpc

require (
	cloud.google.com/go v0.26.0 // indirect
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/client9/misspell v0.3.4
	github.com/golang/glog v0.0.0-20160126235308-23def4e6c14b
	github.com/golang/mock v1.1.1
	github.com/golang/protobuf v1.2.0
	github.com/google/go-cmp v0.2.0
	golang.org/x/lint v0.0.0-20190313153728-d0100b6bd8b3
	golang.org/x/net v0.0.0-20190311183353-d8887717615a
	golang.org/x/oauth2 v0.0.0-20180821212333-d2e6202438be
	golang.org/x/sys v0.0.0-20190215142949-d0b11bdaac8a
	golang.org/x/tools v0.0.0-20190524140312-2c0ae7006135
	google.golang.org/appengine v1.1.0 // indirect
	google.golang.org/genproto v0.0.0-20180817151627-c66870c02cf8
	honnef.co/go/tools v0.0.0-20190523083050-ea95bdfd59fc
)
