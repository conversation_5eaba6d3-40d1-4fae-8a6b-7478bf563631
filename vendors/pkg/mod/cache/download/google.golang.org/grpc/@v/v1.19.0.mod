module google.golang.org/grpc

require (
	cloud.google.com/go v0.26.0 // indirect
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/client9/misspell v0.3.4
	github.com/golang/glog v0.0.0-20160126235308-23def4e6c14b
	github.com/golang/mock v1.1.1
	github.com/golang/protobuf v1.2.0
	golang.org/x/lint v0.0.0-20181026193005-c67002cb31c3
	golang.org/x/net v0.0.0-20180826012351-8a410e7b638d
	golang.org/x/oauth2 v0.0.0-20180821212333-d2e6202438be
	golang.org/x/sync v0.0.0-20180314180146-1d60e4601c6f // indirect
	golang.org/x/sys v0.0.0-20180830151530-49385e6e1522
	golang.org/x/text v0.3.0 // indirect
	golang.org/x/tools v0.0.0-20190114222345-bf090417da8b
	google.golang.org/appengine v1.1.0 // indirect
	google.golang.org/genproto v0.0.0-20180817151627-c66870c02cf8
	honnef.co/go/tools v0.0.0-20190102054323-c2f93a96b099
)
