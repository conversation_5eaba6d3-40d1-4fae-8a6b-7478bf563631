// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//+build go1.13

package xerrors_test

import (
	"errors"
	"testing"

	"golang.org/x/xerrors"
)

func TestErrorsIs(t *testing.T) {
	var errSentinel = errors.New("sentinel")

	got := errors.Is(xerrors.Errorf("%w", errSentinel), errSentinel)
	if !got {
		t.<PERSON>rror("got false, want true")
	}

	got = errors.Is(xerrors.<PERSON><PERSON><PERSON>("%w: %s", errSentinel, "foo"), errS<PERSON>inel)
	if !got {
		t.<PERSON><PERSON>r("got false, want true")
	}
}
