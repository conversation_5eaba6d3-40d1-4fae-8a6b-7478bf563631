package main

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"
)

type RestTFClient struct {
	baseURL    string
	httpClient *http.Client
}

type PredictRequest struct {
	Instances [][]float64 `json:"instances"`
}

type PredictResponse struct {
	Predictions [][]float64 `json:"predictions"`
}

type ModelStatus struct {
	ModelVersionStatus []struct {
		Version string `json:"version"`
		State   string `json:"state"`
		Status  struct {
			ErrorCode    string `json:"error_code"`
			ErrorMessage string `json:"error_message"`
		} `json:"status"`
	} `json:"model_version_status"`
}

func NewRestTFClient(serverAddr string) *RestTFClient {
	// TensorFlow Serving REST API 默认端口是 8501
	if serverAddr == "localhost:8500" {
		serverAddr = "localhost:8501"
	}

	baseURL := fmt.Sprintf("http://%s/v1/models", serverAddr)

	return &RestTFClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (c *RestTFClient) GetModelStatus(modelName string) (*ModelStatus, error) {
	url := fmt.Sprintf("%s/%s", c.baseURL, modelName)

	log.Printf("检查模型状态: %s", url)

	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	var status ModelStatus
	if err := json.NewDecoder(resp.Body).Decode(&status); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &status, nil
}

func (c *RestTFClient) Predict(modelName string, version string, instances [][]float64) (*PredictResponse, error) {
	// 构造 URL
	var url string
	if version != "" {
		url = fmt.Sprintf("%s/%s/versions/%s:predict", c.baseURL, modelName, version)
	} else {
		url = fmt.Sprintf("%s/%s:predict", c.baseURL, modelName)
	}

	// 准备请求数据
	request := PredictRequest{
		Instances: instances,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	log.Printf("发送预测请求: %s", url)
	log.Printf("请求数据: %s", string(jsonData))

	// 发送 HTTP POST 请求
	resp, err := c.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response PredictResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

func main() {
	var serverAddr = flag.String("server_addr", "localhost:8501", "TensorFlow Serving REST API 地址")
	var modelName = flag.String("model_name", "dnn_winr_v1", "模型名称")
	var modelVersion = flag.String("model_version", "", "模型版本 (空表示最新版本)")
	flag.Parse()

	log.Println("=== TensorFlow Serving REST 客户端 ===")
	log.Printf("服务器: %s", *serverAddr)
	log.Printf("模型: %s", *modelName)
	if *modelVersion != "" {
		log.Printf("版本: %s", *modelVersion)
	}

	// 创建客户端
	client := NewRestTFClient(*serverAddr)

	// 检查模型状态
	log.Println("📋 检查模型状态...")
	status, err := client.GetModelStatus(*modelName)
	if err != nil {
		log.Printf("❌ 获取模型状态失败: %v", err)
		log.Println("")
		log.Println("💡 请确保 TensorFlow Serving 正在运行并启用了 REST API:")
		log.Println("   docker run -p 8500:8500 -p 8501:8501 \\")
		log.Println("     --mount type=bind,source=/path/to/model,target=/models/model_name \\")
		log.Println("     -e MODEL_NAME=model_name -t tensorflow/serving")
		log.Println("")
		log.Printf("   或者尝试: go run %s -server_addr=your_server:8501\n", "rest_tfserving_client.go")
		return
	}

	log.Println("✅ 模型状态:")
	for _, version := range status.ModelVersionStatus {
		log.Printf("  版本 %s: %s", version.Version, version.State)
		if version.Status.ErrorCode != "" {
			log.Printf("    错误: %s", version.Status.ErrorMessage)
		}
	}

	// 准备测试数据
	testData := [][]float64{
		{1.0, 2.0, 3.0, 4.0, 5.0}, // 样本 1
		{2.0, 3.0, 4.0, 5.0, 6.0}, // 样本 2
		{0.5, 1.5, 2.5, 3.5, 4.5}, // 样本 3
	}

	log.Println("📊 发送预测请求...")
	log.Printf("输入数据: %d 个样本，每个 %d 个特征", len(testData), len(testData[0]))

	// 发送预测请求
	response, err := client.Predict(*modelName, *modelVersion, testData)
	if err != nil {
		log.Fatalf("❌ 预测失败: %v", err)
	}

	log.Println("✅ 预测成功!")
	log.Println("🎯 预测结果:")

	for i, prediction := range response.Predictions {
		log.Printf("  样本 %d:", i+1)
		for j, value := range prediction {
			log.Printf("    输出 %d: %.6f", j, value)
		}
	}

	log.Println("")
	log.Println("🚀 客户端功能完整!")
	log.Println("✓ 模型状态检查")
	log.Println("✓ 批量预测请求")
	log.Println("✓ 结果解析显示")
	log.Println("✓ 错误处理")
}
