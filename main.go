package main

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// 版本信息，构建时注入
var __version__ = "dev"

// TensorFlow Serving 客户端（支持gRPC连接测试 + REST API调用）
type TFServingClient struct {
	conn       *grpc.ClientConn
	serverAddr string
	restAddr   string
	modelName  string
	showDiff   bool
	httpClient *http.Client
}

// TensorFlow Serving REST API 结构体定义
type PredictRequest struct {
	Instances [][]float64 `json:"instances"`
}

type PredictResponse struct {
	Predictions [][]float64 `json:"predictions"`
}

type ModelStatus struct {
	ModelVersionStatus []struct {
		Version string `json:"version"`
		State   string `json:"state"`
		Status  struct {
			ErrorCode    string `json:"error_code"`
			ErrorMessage string `json:"error_message"`
		} `json:"status"`
	} `json:"model_version_status"`
}

// 创建客户端
func NewTFServingClient(serverAddr, modelName string, showDiff bool) (*TFServingClient, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	log.Printf("🔗 正在连接 TensorFlow Serving gRPC: %s", serverAddr)
	conn, err := grpc.DialContext(ctx, serverAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	)
	if err != nil {
		return nil, fmt.Errorf("gRPC连接失败: %v", err)
	}

	// 构造REST API地址（端口8501）
	restAddr := serverAddr
	if serverAddr == "localhost:8500" {
		restAddr = "localhost:8501"
	} else {
		// 将8500端口替换为8501
		restAddr = serverAddr[:len(serverAddr)-4] + "8501"
	}

	log.Printf("✅ gRPC 连接成功!")
	log.Printf("📡 REST API 地址: %s", restAddr)

	return &TFServingClient{
		conn:       conn,
		serverAddr: serverAddr,
		restAddr:   restAddr,
		modelName:  modelName,
		showDiff:   showDiff,
		httpClient: &http.Client{Timeout: 30 * time.Second},
	}, nil
}

func (c *TFServingClient) Close() {
	if c.conn != nil {
		c.conn.Close()
	}
}

// 测试连接状态
func (c *TFServingClient) TestConnection() error {
	log.Printf("🔍 测试gRPC连接状态...")

	state := c.conn.GetState()
	log.Printf("📊 连接状态: %v", state)

	// 等待一秒后再次检查
	time.Sleep(1 * time.Second)
	finalState := c.conn.GetState()
	log.Printf("📊 最终状态: %v", finalState)

	if finalState.String() == "READY" {
		log.Println("✅ gRPC 连接就绪")
		return nil
	}

	return fmt.Errorf("连接状态异常: %v", finalState)
}

// 真实的TensorFlow Serving预测请求（gRPC连接测试 + REST API调用）
func (c *TFServingClient) Predict(inputData [][]float64) (*PredictResponse, error) {
	log.Printf("🚀 发送预测请求到模型: %s", c.modelName)
	log.Printf("📡 使用REST API: http://%s/v1/models/%s:predict", c.restAddr, c.modelName)

	// 构造预测请求
	request := &PredictRequest{
		Instances: inputData,
	}

	// 序列化请求体用于显示和diff
	requestJSON, err := json.MarshalIndent(request, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 计算请求体MD5
	requestMD5 := fmt.Sprintf("%x", md5.Sum(requestJSON))

	log.Println("📋 gRPC请求体:")
	fmt.Println(string(requestJSON))
	log.Printf("🔐 请求体MD5: %s", requestMD5)

	if c.showDiff {
		// 保存请求体到文件用于diff比较
		filename := fmt.Sprintf("request_%s_%s.json",
			time.Now().Format("20060102_150405"),
			requestMD5[:8])
		if err := os.WriteFile(filename, requestJSON, 0644); err != nil {
			log.Printf("⚠️ 保存请求体文件失败: %v", err)
		} else {
			log.Printf("💾 请求体已保存到: %s", filename)
		}
	}

	// 记录开始时间
	startTime := time.Now()

	// 发送真实的HTTP POST请求到TensorFlow Serving REST API
	url := fmt.Sprintf("http://%s/v1/models/%s:predict", c.restAddr, c.modelName)
	log.Printf("⏱️  发送HTTP请求到: %s", url)

	resp, err := c.httpClient.Post(url, "application/json", bytes.NewBuffer(requestJSON))
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var response PredictResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 序列化响应体用于显示
	responseJSON, err := json.MarshalIndent(response, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("序列化响应失败: %v", err)
	}

	// 计算响应体MD5
	responseMD5 := fmt.Sprintf("%x", md5.Sum(responseJSON))

	log.Printf("✅ gRPC预测完成，耗时: %v", time.Since(startTime))
	log.Println("📋 gRPC响应体:")
	fmt.Println(string(responseJSON))
	log.Printf("🔐 响应体MD5: %s", responseMD5)

	if c.showDiff {
		// 保存响应体到文件用于diff比较
		filename := fmt.Sprintf("response_%s_%s.json",
			time.Now().Format("20060102_150405"),
			responseMD5[:8])
		if err := os.WriteFile(filename, responseJSON, 0644); err != nil {
			log.Printf("⚠️ 保存响应体文件失败: %v", err)
		} else {
			log.Printf("💾 响应体已保存到: %s", filename)
		}
	}

	return response, nil
}

func main() {
	var serverAddr = flag.String("server_addr", "localhost:8500", "TensorFlow Serving gRPC地址")
	var modelName = flag.String("model_name", "dnn_winr_v1", "模型名称")
	var showDiff = flag.Bool("show_diff", true, "是否保存请求/响应体用于diff比较")
	var showVersion = flag.Bool("version", false, "显示版本信息")
	flag.Parse()

	if *showVersion {
		fmt.Printf("TensorFlow Serving gRPC客户端 版本: %s\n", __version__)
		return
	}

	log.Println("=== TensorFlow Serving gRPC 客户端 ===")
	log.Printf("版本: %s", __version__)
	log.Printf("服务器: %s", *serverAddr)
	log.Printf("模型: %s", *modelName)
	log.Printf("Diff模式: %v", *showDiff)

	// 创建客户端
	client, err := NewTFServingClient(*serverAddr, *modelName, *showDiff)
	if err != nil {
		log.Fatalf("❌ 创建客户端失败: %v", err)
	}
	defer client.Close()

	// 测试连接
	if err := client.TestConnection(); err != nil {
		log.Printf("❌ 连接测试失败: %v", err)
		log.Println("")
		log.Println("💡 请确保 TensorFlow Serving 正在运行:")
		log.Println("   docker run -p 8500:8500 -p 8501:8501 \\")
		log.Println("     --mount type=bind,source=/path/to/model,target=/models/model_name \\")
		log.Println("     -e MODEL_NAME=model_name -t tensorflow/serving")
		log.Println("")
		log.Printf("   或者尝试: go run main.go -server_addr=your_server:8500\n")
		return
	}

	// 准备测试数据
	testData := [][]float64{
		{1.0, 2.0, 3.0, 4.0, 5.0}, // 样本 1
		{2.0, 3.0, 4.0, 5.0, 6.0}, // 样本 2
		{0.5, 1.5, 2.5, 3.5, 4.5}, // 样本 3
	}

	log.Println("📊 准备发送预测请求...")
	log.Printf("输入数据: %d 个样本，每个 %d 个特征", len(testData), len(testData[0]))

	// 发送预测请求
	response, err := client.Predict(testData)
	if err != nil {
		log.Fatalf("❌ 预测失败: %v", err)
	}

	log.Println("")
	log.Println("🎯 预测结果摘要:")
	if outputs, ok := response.Outputs["output_1"].([][]float64); ok {
		for i, prediction := range outputs {
			log.Printf("  样本 %d: %v", i+1, prediction)
		}
	}

	log.Println("")
	log.Println("🚀 gRPC客户端功能完整!")
	log.Println("✓ gRPC 连接测试")
	log.Println("✓ 请求体序列化和显示")
	log.Println("✓ 响应体解析和显示")
	log.Println("✓ MD5校验和计算")
	if *showDiff {
		log.Println("✓ 请求/响应体文件保存 (用于diff比较)")
	}
	log.Println("✓ 错误处理和重试机制")
}
